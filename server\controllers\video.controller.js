import mongoose from "mongoose";
import {
	getPreviewVideoUrl,
	getSignedUrlForUpload,
	getThumbnailSignedUrls,
} from "../services/awsSpace.js";
import VideoUpload from "../models/VideoUpload.js";
import User from "../models/User.js";
import { addVideoToQueue } from "../services/videoQueue.js";
import {
	USER_FIELD_MAP,
	videoTypeMapping,
	videoTypeStrToNum,
} from "../constants/index.js";

const getVideoDataByType = (user, videoType) => {
	const videoDataMap = {
		[videoTypeMapping.EarlyLife]: user.earlyLifeData,
		[videoTypeMapping.ProfessionalLife]: user.professionalLifeData,
		[videoTypeMapping.CurrentLife]: user.currentLifeData,
	};

	return videoDataMap[videoType] || null;
};

export const uploadVideo = async (req, res) => {
	try {
		const { videoType, contentType, fileName } = req.body;
		if (!videoType || !Object.keys(videoTypeStrToNum).includes(videoType)) {
			return res.status(400).json({
				message: "Invalid video type.",
			});
		}

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (videoData && videoData.videoId) {
			const video = await VideoUpload.findById(videoData.videoId);
			if (!video) {
				return res
					.status(404)
					.json({ message: "Previous video not found." });
			}
			if (
				video.transcriptionStatus === "pending" ||
				video.transcriptionStatus === "processing"
			) {
				return res.status(400).json({
					message: "Video is being processed.",
				});
			}
		}

		const videoId = new mongoose.Types.ObjectId();
		const signedUrl = await getSignedUrlForUpload(
			`SM360/onboarding/${req.user._id}/${videoId}/${fileName}`,
			contentType
		);

		res.status(200).json({
			message: "Signed URL generated successfully.",
			signedUrl,
			videoId,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({
			message: "Error generating signed URL.",
			error,
		});
	}
};

export const confirmUpload = async (req, res) => {
	try {
		const { videoId, videoType } = req.body;

		const video = await VideoUpload.create({
			_id: videoId,
			createdBy: req.user._id,
			videoType: videoType,
		});

		// Update user with the latest video of the given type
		const user = await User.findById(req.user._id);
		const stepKey = "onboardingLifeData";
		const targetField = USER_FIELD_MAP[videoType]?.[stepKey];

		if (user) {
			if (!user[targetField]) {
				user[targetField] = {};
			}
			user[targetField].videoId = videoId;
			await user.save();
		}

		addVideoToQueue({
			video,
			userId: req.user._id,
		});

		res.status(200).json({
			message: "Video upload confirmed and is being processed.",
			video,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({
			message: "Error confirming video upload.",
			error,
		});
	}
};

export const getVideoStatus = async (req, res) => {
	try {
		const { videoType, polling = false } = req.query;

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData?.videoId) {
			return res.status(200).json({
				videoUrl: null,
				transcriptionStatus: "failed",
			});
		}

		const video = await VideoUpload.findById(videoData.videoId);

		if (!video || video.transcriptionStatus === "failed") {
			return res.status(200).json({
				videoUrl: null,
				transcriptionStatus: "failed",
			});
		}

		if (polling) {
			return res.status(200).json({
				transcriptionStatus: video.transcriptionStatus,
			});
		}

		const videoUrl =
			video.transcriptionStatus !== "failed"
				? await getPreviewVideoUrl(req.user._id, videoData.videoId)
				: null;

		res.status(200).json({
			videoUrl,
			transcriptionStatus: video.transcriptionStatus,
		});
	} catch (error) {
		console.error("Error in getVideoStatus:", error);
		res.status(500).json({
			message: "Error fetching video status.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};

export const getVideoThumbnails = async (req, res) => {
	try {
		const { videoType } = req.query;

		if (!videoType) {
			return res.status(400).json({ message: "Video type is required." });
		}

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData || !videoData.videoId) {
			return res.status(404).json({ message: "Video not found." });
		}

		const video = await VideoUpload.findById(videoData.videoId);
		if (!video) {
			return res.status(404).json({ message: "Video record not found." });
		}

		// Get thumbnail URLs if thumbnails are available
		let thumbnailUrls = [];
		if (video.thumbnailKeys && video.thumbnailKeys.length > 0) {
			try {
				thumbnailUrls = await getThumbnailSignedUrls(video.thumbnailKeys);
			} catch (error) {
				console.error("Error generating thumbnail URLs:", error);
			}
		}

		res.status(200).json({
			thumbnailUrls,
			thumbnailStatus: video.thumbnailStatus,
			thumbnailCount: video.thumbnailKeys ? video.thumbnailKeys.length : 0,
		});
	} catch (error) {
		console.error("Error in getVideoThumbnails:", error);
		res.status(500).json({
			message: "Error fetching video thumbnails.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};
