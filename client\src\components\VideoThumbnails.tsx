import { useState, useEffect } from "react";
import {
	Box,
	Group,
	Image,
	Text,
	Stack,
	Skeleton,
	Paper,
	ScrollArea,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import apiClient from "../config/axios";
import type { videoDataType } from "../types";

interface VideoThumbnailsProps {
	videoType: videoDataType;
	onThumbnailClick?: (thumbnailUrl: string) => void;
	showSelection?: boolean;
}

interface ThumbnailData {
	thumbnailUrls: string[];
	thumbnailKeys: string[];
	thumbnailStatus: "pending" | "processing" | "completed" | "failed";
	thumbnailCount: number;
	selectedThumbnailUrl: string | null;
	selectedThumbnailKey: string | null;
}

const VideoThumbnails = ({
	videoType,
	onThumbnailClick,
	showSelection = false,
}: VideoThumbnailsProps) => {
	const [thumbnailData, setThumbnailData] = useState<ThumbnailData | null>(
		null
	);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selecting, setSelecting] = useState(false);

	const fetchThumbnails = async () => {
		try {
			setLoading(true);
			setError(null);

			const response = await apiClient.get("/api/videos/thumbnails", {
				params: { videoType },
			});

			setThumbnailData(response.data);
		} catch (err: unknown) {
			console.error("Error fetching thumbnails:", err);
			const errorMessage = err && typeof err === 'object' && 'response' in err
				? (err as any).response?.data?.message || "Failed to load thumbnails"
				: "Failed to load thumbnails";
			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	};

	const handleThumbnailSelect = async (_thumbnailUrl: string, index: number) => {
		if (!thumbnailData || !showSelection) return;

		try {
			setSelecting(true);

			// Use the actual thumbnail key from the backend
			const thumbnailKey = thumbnailData.thumbnailKeys[index];

			if (!thumbnailKey) {
				throw new Error("Could not determine thumbnail key");
			}

			await apiClient.post("/api/videos/thumbnails/select", {
				videoType,
				thumbnailKey: thumbnailKey,
			});

			// Refresh thumbnail data to get updated selection
			await fetchThumbnails();

			notifications.show({
				title: "Thumbnail Selected",
				message: "Your thumbnail selection has been saved.",
				color: "green",
			});
		} catch (err: unknown) {
			console.error("Error selecting thumbnail:", err);
			const errorMessage = err && typeof err === 'object' && 'response' in err
				? (err as any).response?.data?.message || "Failed to select thumbnail"
				: "Failed to select thumbnail";
			notifications.show({
				title: "Selection Failed",
				message: errorMessage,
				color: "red",
			});
		} finally {
			setSelecting(false);
		}
	};

	useEffect(() => {
		fetchThumbnails();
	}, [videoType]); // eslint-disable-line react-hooks/exhaustive-deps

	if (loading) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Group gap="xs">
					{Array.from({ length: 5 }).map((_, index) => (
						<Skeleton
							key={index}
							height={60}
							width={80}
							radius="sm"
						/>
					))}
				</Group>
			</Box>
		);
	}

	if (error) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="red">
					{error}
				</Text>
			</Box>
		);
	}

	if (!thumbnailData || thumbnailData.thumbnailUrls.length === 0) {
		const statusMessage =
			thumbnailData?.thumbnailStatus === "processing"
				? "Generating thumbnails..."
				: thumbnailData?.thumbnailStatus === "failed"
					? "Failed to generate thumbnails"
					: "No thumbnails available";

		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="dimmed">
					{statusMessage}
				</Text>
			</Box>
		);
	}

	return (
		<Box>
			<Text size="sm" c="dimmed" mb="xs">
				Video Thumbnails ({thumbnailData.thumbnailCount})
				{showSelection && thumbnailData.selectedThumbnailUrl && (
					<Text component="span" size="xs" c="green" ml="xs">
						• Selected
					</Text>
				)}
			</Text>
			<ScrollArea>
				<Group gap="xs" wrap="nowrap">
					{thumbnailData.thumbnailUrls.map((thumbnailUrl, index) => {
						const isSelected = showSelection && thumbnailData.selectedThumbnailUrl === thumbnailUrl;
						const canClick = onThumbnailClick || showSelection;

						return (
							<Paper
								key={index}
								shadow={isSelected ? "md" : "sm"}
								radius="sm"
								style={{
									cursor: canClick ? "pointer" : "default",
									transition: "all 0.2s ease",
									minWidth: 80,
									border: isSelected ? "2px solid var(--mantine-color-blue-6)" : "2px solid transparent",
									opacity: selecting ? 0.7 : 1,
								}}
								onMouseEnter={e => {
									if (canClick && !selecting) {
										e.currentTarget.style.transform = "scale(1.05)";
									}
								}}
								onMouseLeave={e => {
									if (canClick && !selecting) {
										e.currentTarget.style.transform = "scale(1)";
									}
								}}
								onClick={() => {
									if (selecting) return;
									if (showSelection) {
										handleThumbnailSelect(thumbnailUrl, index);
									} else if (onThumbnailClick) {
										onThumbnailClick(thumbnailUrl);
									}
								}}
							>
							<Image
								src={thumbnailUrl}
								alt={`Thumbnail ${index + 1}`}
								height={60}
								width={80}
								fit="cover"
								radius="sm"
								fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAyNUw0NSAzMEwzNSAzNVYyNVoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+"
							/>
						</Paper>
						);
					})}
				</Group>
			</ScrollArea>
		</Box>
	);
};

export default VideoThumbnails;
