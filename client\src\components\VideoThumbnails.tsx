import { useState, useEffect } from "react";
import {
	Box,
	Group,
	Image,
	Text,
	Stack,
	Skeleton,
	Paper,
	ScrollArea,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import apiClient from "../config/axios";
import type { videoDataType } from "../types";

interface VideoThumbnailsProps {
	videoType: videoDataType;
	onThumbnailClick?: (thumbnailUrl: string) => void;
}

interface ThumbnailData {
	thumbnailUrls: string[];
	thumbnailStatus: "pending" | "processing" | "completed" | "failed";
	thumbnailCount: number;
}

const VideoThumbnails = ({
	videoType,
	onThumbnailClick,
}: VideoThumbnailsProps) => {
	const [thumbnailData, setThumbnailData] = useState<ThumbnailData | null>(
		null
	);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const fetchThumbnails = async () => {
		try {
			setLoading(true);
			setError(null);

			const response = await apiClient.get("/api/videos/thumbnails", {
				params: { videoType },
			});

			setThumbnailData(response.data);
		} catch (err: any) {
			console.error("Error fetching thumbnails:", err);
			setError(
				err.response?.data?.message || "Failed to load thumbnails"
			);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchThumbnails();
	}, [videoType]);

	if (loading) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Group gap="xs">
					{Array.from({ length: 5 }).map((_, index) => (
						<Skeleton
							key={index}
							height={60}
							width={80}
							radius="sm"
						/>
					))}
				</Group>
			</Box>
		);
	}

	if (error) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="red">
					{error}
				</Text>
			</Box>
		);
	}

	if (!thumbnailData || thumbnailData.thumbnailUrls.length === 0) {
		const statusMessage =
			thumbnailData?.thumbnailStatus === "processing"
				? "Generating thumbnails..."
				: thumbnailData?.thumbnailStatus === "failed"
					? "Failed to generate thumbnails"
					: "No thumbnails available";

		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="dimmed">
					{statusMessage}
				</Text>
			</Box>
		);
	}

	return (
		<Box>
			<Text size="sm" c="dimmed" mb="xs">
				Video Thumbnails ({thumbnailData.thumbnailCount})
			</Text>
			<ScrollArea>
				<Group gap="xs" wrap="nowrap">
					{thumbnailData.thumbnailUrls.map((thumbnailUrl, index) => (
						<Paper
							key={index}
							shadow="sm"
							radius="sm"
							style={{
								cursor: onThumbnailClick
									? "pointer"
									: "default",
								transition: "transform 0.2s ease",
								minWidth: 80,
							}}
							onMouseEnter={e => {
								if (onThumbnailClick) {
									e.currentTarget.style.transform =
										"scale(1.05)";
								}
							}}
							onMouseLeave={e => {
								if (onThumbnailClick) {
									e.currentTarget.style.transform =
										"scale(1)";
								}
							}}
							onClick={() => onThumbnailClick?.(thumbnailUrl)}
						>
							<Image
								src={thumbnailUrl}
								alt={`Thumbnail ${index + 1}`}
								height={60}
								width={80}
								fit="cover"
								radius="sm"
								fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAyNUw0NSAzMEwzNSAzNVYyNVoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+"
							/>
						</Paper>
					))}
				</Group>
			</ScrollArea>
		</Box>
	);
};

export default VideoThumbnails;
