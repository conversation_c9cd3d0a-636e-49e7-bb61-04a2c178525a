import { Group, Box, Title, Stack, Text, Card } from "@mantine/core";

import { videoTypeLabel } from "../constants";
import type { videoDataType } from "../types";
import VideoThumbnails from "./VideoThumbnails";

interface VideoPreviewAndUploadProps {
	videoPreviewUrl: string | null;
	videoType: videoDataType;
	setHasUnsavedChanges?: (hasUnsavedChanges: boolean) => void;
	editing?: boolean;
}

//remove unused code for upload

const VideoPreviewAndUpload = ({
	videoPreviewUrl,
	videoType,
}: VideoPreviewAndUploadProps) => {
	return (
		<Group justify="space-between" align="center" mb={32} wrap="nowrap">
			<Card
				shadow="sm"
				radius="lg"
				withBorder
				h={"28rem"}
				w={"48%"}
				p={"lg"}
			>
				<Stack h="100%" justify="center">
					<Stack gap={1}>
						<Title order={2}>
							{videoTypeLabel[videoType]} Video
						</Title>
						<Text c="gray">Watch the uploaded video.</Text>
					</Stack>
					<Box
						style={{
							backgroundColor: "black",
							borderRadius: "var(--mantine-radius-md)",
							overflow: "hidden",
							height: "60%",
							width: "100%",
						}}
					>
						<video
							controls
							src={videoPreviewUrl ? videoPreviewUrl : ""}
							style={{
								width: "100%",
								height: "100%",
								objectFit: "contain",
							}}
						/>
					</Box>
					{videoPreviewUrl && (
						<Box mt="md">
							<VideoThumbnails videoType={videoType} />
						</Box>
					)}
				</Stack>
			</Card>
		</Group>
	);
};

export default VideoPreviewAndUpload;
